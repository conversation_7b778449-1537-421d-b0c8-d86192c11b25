#!/usr/bin/env python3
"""
Script to format Python code with all the required tools.
Usage: python format_code.py <file_or_directory>
"""

import subprocess
import sys
from pathlib import Path
from typing import List, Optional


def run_command(cmd: List[str], cwd: Optional[Path] = None) -> bool:
    """Run a command and return True if successful."""
    try:
        result = subprocess.run(
            cmd, cwd=cwd, capture_output=True, text=True, check=True
        )
        print(f"✅ {' '.join(cmd)}")
        if result.stdout:
            print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {' '.join(cmd)} failed:")
        print(e.stderr)
        return False


def format_python_code(target: Path) -> bool:
    """Format Python code with isort, black, and check with mypy."""
    success = True
    
    print(f"🔧 Formatting Python code in: {target}")
    
    # Step 1: Sort imports with isort
    print("\n1. Sorting imports with isort...")
    success &= run_command(["poetry", "run", "isort", str(target)])
    
    # Step 2: Format code with black
    print("\n2. Formatting code with black...")
    success &= run_command(["poetry", "run", "black", str(target)])
    
    # Step 3: Type check with mypy
    print("\n3. Type checking with mypy...")
    success &= run_command(["poetry", "run", "mypy", str(target)])
    
    if success:
        print("\n✅ All formatting and checks passed!")
    else:
        print("\n❌ Some checks failed. Please review the output above.")
    
    return success


def main() -> None:
    """Main function."""
    if len(sys.argv) != 2:
        print("Usage: python format_code.py <file_or_directory>")
        sys.exit(1)
    
    target = Path(sys.argv[1])
    if not target.exists():
        print(f"Error: {target} does not exist")
        sys.exit(1)
    
    success = format_python_code(target)
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
