import os
import sys
from pathlib import Path
from typing import Dict, List, Optional


def process_data(data: List[Dict[str, str]], filter_key: Optional[str] = None) -> List[Dict[str, str]]:
    """Process data with optional filtering."""
    if filter_key is None:
        return data
    
    result: List[Dict[str, str]] = []
    for item in data:
        if filter_key in item:
            result.append(item)
    
    return result


class DataProcessor:
    """A class for processing data with type annotations."""
    
    def __init__(self, config: Dict[str, str]) -> None:
        self.config = config
        self.processed_count: int = 0
    
    def process_file(self, file_path: Path) -> bool:
        """Process a single file."""
        if not file_path.exists():
            return False
        
        self.processed_count += 1
        return True
    
    def get_stats(self) -> Dict[str, int]:
        """Get processing statistics."""
        return {"processed_count": self.processed_count}
